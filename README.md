# Discord Bybit Signal Monitor

Bot Discord do monitorowania sygnałów tradingowych i automatycznego śledzenia ich wyników na giełdzie Bybit.

## Funkcjonalności

- 🔍 **Automatyczne wykrywanie sygnałów** z wiadomości Discord
- 📊 **Monitoring cen w czasie rzeczywistym** przez API Bybit
- 🎯 **Automatyczne zamykanie pozycji** przy osiągnięciu TP/SL lub timeout
- 📈 **Statystyki wydajności** sygnałów
- 💾 **Baza danych SQLite** do przechowywania historii
- 🌐 **Dashboard Web** z wykresami i analizą w czasie rzeczywistym
- 📥 **Import historycznych sygnałów** z Discord
- 📊 **Eksport danych** do CSV

## Wymagania

- Python 3.8+
- Konto Discord Developer (dla bota)
- Konto Bybit z API key (opcjonalne dla testów)

## Instalacja

1. **Sklonuj/pobierz projekt**

```bash
git clone <repository_url>
cd discord-bybit-signal-monitor
```

2. **Zainstaluj zależ<PERSON>ści**

```bash
pip install -r requirements.txt
```

3. **Skonfiguruj zmienne środowiskowe**

```bash
cp .env.example .env
```

Edytuj plik `.env` i uzupełnij:

- `DISCORD_TOKEN` - token bota Discord
- `DISCORD_CHANNEL_ID` - ID kanału do monitorowania
- `BYBIT_API_KEY` i `BYBIT_API_SECRET` - klucze API Bybit

## Konfiguracja Discord Bot

1. Idź na https://discord.com/developers/applications
2. Utwórz nową aplikację i bota
3. Skopiuj token bota do `.env`
4. Włącz "Message Content Intent" w ustawieniach bota
5. Dodaj bota do serwera z uprawnieniami:
   - Read Messages
   - Send Messages
   - Read Message History

## Konfiguracja Bybit API

1. Zaloguj się na Bybit
2. Idź do API Management
3. Utwórz nowy API key z uprawnieniami do odczytu (Read)
4. Skopiuj klucze do `.env`

## Uruchomienie

### Bot Discord

```bash
python discord_bybit_signal_monitor.py
```

### Dashboard Web

```bash
python dashboard.py
```

Dashboard będzie dostępny pod adresem: http://localhost:5000

### Import historycznych sygnałów

```bash
python import_discord_history.py
```

### Dodanie przykładowych danych (do testów)

```bash
python add_sample_signals.py
```

## Format sygnałów

Bot rozpoznaje sygnały w formacie:

```
BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60
```

## Komendy bota

- `!stats` - wyświetla statystyki sygnałów

## Dashboard Web

Dashboard oferuje zaawansowaną analizę sygnałów:

### Funkcjonalności Dashboard:

- 📊 **Statystyki w czasie rzeczywistym** - win rate, średni PnL, liczba sygnałów
- 📈 **Wykresy interaktywne** - PnL w czasie, rozkład wyników
- 🔍 **Filtrowanie sygnałów** - po parze, statusie, dacie
- 📋 **Tabela sygnałów** - szczegółowy widok wszystkich sygnałów
- 📥 **Eksport do CSV** - pobieranie danych do analizy
- 🔴 **Live updates** - automatyczne odświeżanie przy nowych sygnałach

### Dostęp do Dashboard:

1. Uruchom dashboard: `python dashboard.py`
2. Otwórz przeglądarkę: http://localhost:5000
3. Dashboard automatycznie połączy się z bazą danych

## Struktura bazy danych

Sygnały są przechowywane w tabeli `signals` z polami:

- `id` - unikalny identyfikator
- `message_id` - ID wiadomości Discord
- `pair` - para handlowa (np. BTCUSDT)
- `side` - kierunek (BUY/SELL)
- `entry` - cena wejścia
- `tp` - take profit
- `sl` - stop loss
- `timestamp` - czas utworzenia
- `timeframe_min` - czas życia sygnału w minutach
- `status` - status (open/tp/sl/timeout)
- `close_timestamp` - czas zamknięcia
- `exit_price` - cena wyjścia
- `pnl` - zysk/strata w %

## Bezpieczeństwo

⚠️ **UWAGA**: Bot tylko monitoruje ceny i nie wykonuje rzeczywistych transakcji. Służy wyłącznie do analizy wydajności sygnałów.

## Rozwiązywanie problemów

### Błąd "Missing required environment variables"

- Sprawdź czy plik `.env` istnieje i zawiera wszystkie wymagane zmienne

### Błąd "401 Unauthorized"

- Sprawdź poprawność tokena Discord
- Upewnij się, że bot ma włączony "Message Content Intent"

### Błąd połączenia z Bybit

- Sprawdź poprawność kluczy API
- Upewnij się, że klucze mają uprawnienia do odczytu

## Licencja

MIT License
